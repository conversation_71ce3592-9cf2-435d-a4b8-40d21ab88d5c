<script setup lang="ts">
import { format, useLoading } from '@idmy/core'
import { check, getLast } from '@mh-bcs/util'
import { Button, Card, Col, Descriptions, Divider, Input, message, Modal, Row, Statistic } from 'ant-design-vue'
import { computed, onMounted, reactive, ref } from 'vue'
import RevenueChart from './Charts/RevenueChart.vue'
import InsuranceChart from './Charts/InsuranceChart.vue'
import BusinessChart from './Charts/BusinessChart.vue'
import TrendChart from './Charts/TrendChart.vue'
import OtherStatsChart from './Charts/OtherStatsChart.vue'

// 获取当前日期时间
const currentDateTime = ref(new Date())

// 交账相关数据
const accountSubmission = reactive({
  lastCheckTime: '',
  endTime: format(new Date(), 'Date', 'YYYY-MM-DD HH:mm:ss'),
  cashAmount: 0,
  showModal: false,
  submitting: false,
  settlementInfo: {
    charge: 1595.8,
    refund: -1595.8,
    actualCharge: 0.0,
    insurance: 0.0,
    pooledFund: 0.0,
    depositUsed: 0.0,
    selfPay: 0.0,
    pricingForms: 72,
  },
})

// 计算属性：是否允许交账
const canSubmitAccount = computed(() => {
  return accountSubmission.cashAmount >= 0
})

// 这里可以添加数据获取逻辑，目前使用静态数据展示
const cashierStats = {
  deposit: 0,
  totalAmount: 0,
  discount: 0,
  charge: ********.51,
  refund: -53214.85,
  actualCharge: ********.66,
  insurance: 5600629.54,
  pooledFund: 100.54,
  personalAccount: 100,
  mutualAid: 100,
  selfPay: 4562750.12,
  pricingForms: 264977,
  outpatientFee: 8500000,
  inpatientFee: 1200000,
  registrationFee: 463179.66,
  unsubmitted: 15,
  submitted: 8,
  prepayment: 2500000,
}

// 获取上次交账信息
const fetchLastCheckInfo = async () => {
  try {
    const result = await getLast()
    if (result && result.timeChecked) {
      accountSubmission.lastCheckTime = format(result.timeChecked, 'Date', 'YYYY-MM-DD HH:mm:ss')
    }
  } catch (error) {
    console.error('获取上次交账信息失败:', error)
  }
}

// 打开交账确认弹窗
const openSubmitModal = () => {
  accountSubmission.showModal = true
}

// 关闭交账确认弹窗
const closeSubmitModal = () => {
  accountSubmission.showModal = false
}

// 提交交账
const [submitAccount, submitting] = useLoading(async () => {
  try {
    const result = await check(accountSubmission.cashAmount, accountSubmission.endTime)
    message.success('交账成功！')
    closeSubmitModal()
    // 刷新上次交账信息
    fetchLastCheckInfo()
    return result
  } catch (error) {
    message.error('交账失败：' + (error.message || '未知错误'))
    return null
  }
})

// 模拟趋势数据（最近7天）
const trendData = ref([
  { date: '05-22', charge: 8500000, refund: -45000, actualCharge: 8455000 },
  { date: '05-23', charge: 9200000, refund: -38000, actualCharge: 9162000 },
  { date: '05-24', charge: 7800000, refund: -52000, actualCharge: 7748000 },
  { date: '05-25', charge: ********, refund: -41000, actualCharge: ******** },
  { date: '05-26', charge: 9800000, refund: -47000, actualCharge: 9753000 },
  { date: '05-27', charge: ********, refund: -55000, actualCharge: ******** },
  { date: '05-28', charge: cashierStats.charge, refund: cashierStats.refund, actualCharge: cashierStats.actualCharge },
])

// 计算图表数据
const revenueChartData = computed(() => ({
  charge: cashierStats.charge,
  refund: cashierStats.refund,
  actualCharge: cashierStats.actualCharge
}))

const insuranceChartData = computed(() => ({
  insurance: cashierStats.insurance,
  pooledFund: cashierStats.pooledFund,
  personalAccount: cashierStats.personalAccount,
  mutualAid: cashierStats.mutualAid,
  selfPay: cashierStats.selfPay
}))

const businessChartData = computed(() => ({
  outpatientFee: cashierStats.outpatientFee,
  inpatientFee: cashierStats.inpatientFee,
  registrationFee: cashierStats.registrationFee
}))

const otherStatsData = computed(() => ({
  pricingForms: cashierStats.pricingForms,
  unsubmitted: cashierStats.unsubmitted,
  submitted: cashierStats.submitted,
  prepayment: cashierStats.prepayment
}))

// 组件挂载时获取上次交账信息
onMounted(() => {
  fetchLastCheckInfo()
})
</script>

<template>
  <div class="cashier-stats-container">
    <Row :gutter="[16, 16]">
      <Col :span="6">
        <Card title="收费概览" class="stats-card charts-card" size="small">
          <RevenueChart :data="revenueChartData" h-300px />
        </Card>
      </Col>

      <Col :span="6">
        <Card title="医保分布" class="stats-card charts-card" size="small">
          <InsuranceChart :data="insuranceChartData" h-300px/>
        </Card>
      </Col>

      <Col :span="6">
        <Card title="费用分类" class="stats-card charts-card" size="small">
          <BusinessChart :data="businessChartData" h-300px/>
        </Card>
      </Col>

      <Col :span="6">
        <Card title="其他统计" class="stats-card charts-card" size="small">
          <OtherStatsChart :data="otherStatsData" />
        </Card>
      </Col>

      <!-- 交账卡片 -->
      <Col :span="24">
        <Card title="我的交账" class="stats-card account-submission-card">
          <div class="account-submission-header">
            <div class="account-submission-title">未交账统计( {{ accountSubmission.endTime }} )</div>
            <div class="account-submission-subtitle">允许当天多次交账（统计数据包含：挂号、门诊、预交金、住院等全部费用）</div>
          </div>

          <div class="account-submission-content">
            <Row :gutter="[24, 16]">
              <Col :span="12">
                <div class="account-info-section">
                  <div class="account-info-item">
                    <span class="account-info-label">上次交账日期</span>
                    <span class="account-info-value">{{ accountSubmission.lastCheckTime || '无' }}</span>
                  </div>
                  <div class="account-info-item">
                    <span class="account-info-label">本次截止时间</span>
                    <span class="account-info-value">{{ accountSubmission.endTime }}</span>
                  </div>
                </div>

                <Divider class="account-divider" />

                <div class="settlement-info-section">
                  <div class="section-title">本次结算信息</div>
                  <div class="settlement-info-item">
                    <span class="settlement-info-label">收费：</span>
                    <span class="settlement-info-value highlight">{{ accountSubmission.settlementInfo.charge.toLocaleString() }}</span>
                  </div>
                  <div class="settlement-info-item">
                    <span class="settlement-info-label">退费：</span>
                    <span class="settlement-info-value negative">{{ accountSubmission.settlementInfo.refund.toLocaleString() }}</span>
                  </div>
                  <div class="settlement-info-item">
                    <span class="settlement-info-label">实收：</span>
                    <span class="settlement-info-value highlight">{{ accountSubmission.settlementInfo.actualCharge.toLocaleString() }}</span>
                  </div>
                  <div class="settlement-info-item">
                    <span class="settlement-info-label">医保：</span>
                    <span class="settlement-info-value">{{ accountSubmission.settlementInfo.insurance.toLocaleString() }}</span>
                  </div>
                  <div class="settlement-info-item">
                    <span class="settlement-info-label">统筹：</span>
                    <span class="settlement-info-value">{{ accountSubmission.settlementInfo.pooledFund.toLocaleString() }}</span>
                  </div>
                  <div class="settlement-info-item">
                    <span class="settlement-info-label">预交金使用：</span>
                    <span class="settlement-info-value">{{ accountSubmission.settlementInfo.depositUsed.toLocaleString() }}</span>
                  </div>
                  <div class="settlement-info-item">
                    <span class="settlement-info-label">自费：</span>
                    <span class="settlement-info-value">{{ accountSubmission.settlementInfo.selfPay.toLocaleString() }}</span>
                  </div>
                  <div class="settlement-info-item">
                    <span class="settlement-info-label">划价单数：</span>
                    <span class="settlement-info-value">{{ accountSubmission.settlementInfo.pricingForms }}</span>
                  </div>
                </div>
              </Col>

              <Col :span="12">
                <div class="cash-submission-section">
                  <div class="cash-input-container">
                    <div class="cash-input-label">本次上缴现金</div>
                    <div class="cash-input-wrapper">
                      <Input v-model:value="accountSubmission.cashAmount" class="cash-input" placeholder="请输入上缴现金金额" type="number" suffix="元" />
                    </div>
                  </div>

                  <div class="submit-button-container">
                    <Button type="primary" size="large" class="submit-button" :disabled="!canSubmitAccount" @click="openSubmitModal"> 交账 </Button>
                  </div>
                </div>
              </Col>
            </Row>
          </div>
        </Card>
      </Col>
    </Row>

    <!-- 交账确认弹窗 -->
    <Modal v-model:visible="accountSubmission.showModal" title="交账确认" @cancel="closeSubmitModal" :maskClosable="false">
      <div class="confirm-modal-content">
        <p>您确定要提交交账吗？</p>
        <Descriptions bordered size="small" class="confirm-descriptions">
          <Descriptions.Item label="上次交账时间" :span="3">{{ accountSubmission.lastCheckTime || '无' }}</Descriptions.Item>
          <Descriptions.Item label="本次截止时间" :span="3">{{ accountSubmission.endTime }}</Descriptions.Item>
          <Descriptions.Item label="收费" :span="1">{{ accountSubmission.settlementInfo.charge.toLocaleString() }}</Descriptions.Item>
          <Descriptions.Item label="退费" :span="1">{{ accountSubmission.settlementInfo.refund.toLocaleString() }}</Descriptions.Item>
          <Descriptions.Item label="实收" :span="1">{{ accountSubmission.settlementInfo.actualCharge.toLocaleString() }}</Descriptions.Item>
          <Descriptions.Item label="本次上缴现金" :span="3">
            <span class="highlight-text">{{ accountSubmission.cashAmount }} 元</span>
          </Descriptions.Item>
        </Descriptions>
      </div>

      <template #footer>
        <Button @click="closeSubmitModal">取消</Button>
        <Button type="primary" :loading="submitting" @click="submitAccount">确认交账</Button>
      </template>
    </Modal>
  </div>
</template>

<style scoped lang="less">

.stats-card {
  height: 100%;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;

  :deep(.ant-card-head) {
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
  }

  :deep(.ant-statistic) {
    margin-bottom: 16px;
  }

  :deep(.ant-statistic-title) {
    color: #666;
    font-size: 14px;
  }

  :deep(.ant-statistic-content) {
    font-size: 16px;
    color: #333;
  }

  :deep(.highlight-stat .ant-statistic-content) {
    color: #009b9b;
    font-weight: 500;
  }

  :deep(.negative-stat .ant-statistic-content) {
    color: #ff4d4f;
  }
}

// 图表卡片样式
.charts-card {
  margin-top: 8px;

  :deep(.ant-card-body) {
    padding: 8px;
  }

  :deep(.ant-tabs-content-holder) {
    padding: 8px 0;
  }

  :deep(.ant-tabs-tab) {
    font-size: 14px;
  }
}

// 交账卡片样式
.account-submission-card {
  margin-top: 8px;

  :deep(.ant-card-body) {
    padding: 20px;
  }
}

.account-submission-header {
  margin-bottom: 20px;
}

.account-submission-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.account-submission-subtitle {
  font-size: 14px;
  color: #666;
}

.account-info-section {
  margin-bottom: 16px;
}

.account-info-item {
  display: flex;
  margin-bottom: 12px;
}

.account-info-label {
  width: 120px;
  color: #666;
  font-size: 14px;
}

.account-info-value {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.account-divider {
  margin: 16px 0;
}

.settlement-info-section {
  margin-bottom: 16px;
}

.section-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
}

.settlement-info-item {
  display: flex;
  margin-bottom: 12px;
}

.settlement-info-label {
  width: 120px;
  color: #666;
  font-size: 14px;
}

.settlement-info-value {
  flex: 1;
  font-size: 14px;
  color: #333;

  &.highlight {
    color: #009b9b;
    font-weight: 500;
  }

  &.negative {
    color: #ff4d4f;
  }
}

.cash-submission-section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.cash-input-container {
  margin-bottom: 30px;
}

.cash-input-label {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
}

.cash-input-wrapper {
  width: 100%;
}

.cash-input {
  height: 50px;
  font-size: 16px;

  :deep(.ant-input) {
    text-align: center;
    font-size: 18px;
  }

  :deep(.ant-input-suffix) {
    font-size: 16px;
    color: #666;
  }
}

.submit-button-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.submit-button {
  width: 180px;
  height: 50px;
  font-size: 16px;
}

// 确认弹窗样式
.confirm-modal-content {
  p {
    font-size: 16px;
    margin-bottom: 20px;
  }
}

.confirm-descriptions {
  margin-bottom: 20px;

  :deep(.ant-descriptions-item-label) {
    width: 120px;
    background-color: #fafafa;
  }
}

.highlight-text {
  color: #009b9b;
  font-weight: 500;
  font-size: 16px;
}
</style>
